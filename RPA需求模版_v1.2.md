---
文档标题: RPA需求申请表（简化版）
创建日期: 2025-07-29
修订日期: 2025-08-14
版本: v1.2
适用范围: 广州融贸RPA项目组
RPA供应商: 实在RPA（实在智能）
---
# RPA需求申请表

## 1. 基本信息

### 1.1 需求基础信息

| 字段                   | 内容   | 备注（正式文档请删除此列）                                                                      |
| ---------------------- | ------ | ----------------------------------------------------------------------------------------------- |
| **需求编号**     | [必填] | 按照开发规范编写                                                                                |
| **需求标题**     | [必填] | 简洁描述需求内容，控制在20字以内                                                                |
| **提出部门**     | [必填] | 选择：供应链/市场/客服/财务/技术/人事/运营/仓储/采购                                            |
| **需求提出人**   | [必填] | 填写姓名和联系方式                                                                              |
| **提出日期**     | [必填] | YYYY-MM-DD格式                                                                                  |
| **期望完成日期** | [必填] | YYYY-MM-DD格式                                                                                  |
| **业务类型**     | [必填] | 选择：订单处理/库存管理/报表生成/客户管理/财务处理/物流跟踪/产品管理/营销推广/质量管控/数据同步 |

### 1.2 优先级评估

🔴 **必须** - 请根据业务影响程度选择优先级：

- [ ] **L1 - 最高优先级**: 影响核心业务，故障会导致业务中断，需要立即处理
- [ ] **L2 - 中等优先级**: 提升工作效率，故障影响有限，需要及时处理
- [ ] **L3 - 一般优先级**: 辅助功能，故障不影响主要业务，可延后处理

**选择理由**: [说明为什么选择这个重要程度]

### 1.3 执行时间

请选择希望机器人什么时候执行这个工作：

🔴 **必须** - 请选择流程执行方式：

**定时执行类型：**

- [ ] 每日一次 - 具体时间：____点____分
- [ ] 每日两次 - 具体时间：____点____分 和 ____点____分
- [ ] 每日四次 - 具体时间：____点、____点、____点、____点
- [ ] 每日六次 - 具体时间：____点、____点、____点、____点、____点、____点
- [ ] 每周一次 - 具体时间：星期____，____点____分
- [ ] 每月一次 - 具体时间：每月____号，____点____分

**固定时点执行：**

- [ ] 每日8点
- [ ] 每日12点
- [ ] 每日18点
- [ ] 每日22点
- [ ] 工作日9点
- [ ] 工作日17点

**其他方式：**

- [ ] 手动启动 - 什么时候需要手动启动：________________
- [ ] 每周执行 - 星期____，____点____分

## 2. 工作描述

### 2.1 现在是怎么做的

🔴 **必须** -请详细描述现在这个工作是怎么做的：

```
使用文字描述关键的工作流程
```

**工作步骤**：

| 步骤 | 具体操作                | 用什么系统 | 大概用时 | 有什么问题       |
| ---- | ----------------------- | ---------- | -------- | ---------------- |
| 1    | [比如：打开亚马逊后台]  | [系统名称] | [分钟]   | [比如：经常卡顿] |
| 2    | [比如：下载订单数据]    | [系统名称] | [分钟]   | [比如：下载很慢] |
| 3    | [比如：整理数据到Excel] | [系统名称] | [分钟]   | [比如：容易出错] |
| 4    | [比如：发送给相关同事]  | [系统名称] | [分钟]   | [比如：忘记发送] |
| ...  | [继续添加步骤]          | [...]      | [...]    | [...]            |

**工作量统计**：

| 项目         | 数量    | 说明                       |
| ------------ | ------- | -------------------------- |
| 每次用时     | ___分钟 | 从开始到结束需要多长时间   |
| 每天做几次   | ___次   | 一天要做几次这个工作       |
| 每天总用时   | ___小时 | 一天在这个工作上花多少时间 |
| 有几个人在做 | ___人   | 有多少人在做这个工作       |

### 2.2 希望机器人怎么做

🔴 **必须** -请详细说明希望机器人自动完成哪些操作：

```
人工截图描述和流程记录器导出pdf二选一
```

**机器人操作步骤**：

| 步骤 | 操作名称     | 具体怎么操作                               | 处理什么数据           | 截图或说明 | 注意事项             |
| ---- | ------------ | ------------------------------------------ | ---------------------- | ---------- | -------------------- |
| 1    | [比如：登录] | [比如：打开网站，输入用户名密码，点击登录] | [比如：用户名和密码]   | [可贴截图] | [比如：密码可能过期] |
| 2    | [比如：下载] | [比如：点击下载按钮，选择日期范围]         | [比如：昨天的订单数据] | [可贴截图] | [比如：周末没有数据] |
| 3    | [比如：整理] | [比如：打开Excel，复制粘贴数据，计算总数]  | [比如：订单金额]       | [可贴截图] | [比如：格式要统一]   |
| 4    | [比如：发送] | [比如：发邮件给相关同事]                   | [比如：整理好的报表]   | [可贴截图] | [比如：抄送给经理]   |

**流程记录器:** ___________________(流程记录器导出pdf附件)

**数据说明**：

- **数据从哪来**: ________________（比如：亚马逊后台订单页面）
- **数据是什么格式**: ________________（比如：Excel表格，CSV文件）
- **大概多少数据**: 每次处理约____条数据
- **数据举例**: [请提供1-2个具体的数据例子，比如订单号、金额等]

**最终要得到什么结果**：
[请说明机器人完成后应该产生什么，比如：生成每日销售报表并发送给销售经理]

## 3. 系统信息

### 3.1 需要用到的系统

请提供机器人需要操作的系统信息：

**系统登录信息**：

| 系统名称       | 网址                             | 怎么登录         | 需要什么权限     |
| -------------- | -------------------------------- | ---------------- | ---------------- |
| [比如：亚马逊] | [比如：sellercentral.amazon.com] | [用户名密码登录] | [查看和下载订单] |
| [系统2]        | [网址]                           | [登录方式]       | [需要的权限]     |

### 3.2 文件处理

**文件要求**：

| 文件用途 | 文件格式    | 保存位置         | 文件名格式             |
| -------- | ----------- | ---------------- | ---------------------- |
| 原始数据 | [Excel/CSV] | [比如：D:\数据\] | [比如：订单_日期.xlsx] |
| 处理结果 | [Excel/CSV] | [比如：D:\报表\] | [比如：报表_日期.xlsx] |

### 3.3 出现问题怎么办

🟡 **建议** - 考虑可能出现的异常情况：

**常见问题处理**：

| 可能出现的问题 | 怎么处理      | 要不要通知人 | 通知方式             |
| -------------- | ------------- | ------------ | -------------------- |
| 网络连接失败   | 重试3次       | 失败后通知   | [邮件/短信/系统通知] |
| 登录失败       | 重试2次       | 失败后通知   | [邮件/短信/系统通知] |
| 找不到数据     | 跳过继续      | 记录日志     | [邮件/短信/系统通知] |
| 系统维护中     | 等待1小时重试 | 通知相关人员 | [邮件/短信/系统通知] |
| ...            | ...           | ...          | ...                  |

---

## 使用说明

**填写提示**：

### 重要程度说明

- **非常重要**：不做这个工作会影响正常业务，比如订单处理、库存更新等
- **比较重要**：能明显提高工作效率，减少重复劳动
- **一般重要**：有了更好，但暂时没有也不影响主要工作

### 填写技巧

1. **需求标题**：用一句话说清楚要做什么，比如"每日亚马逊订单数据导出"
2. **工作步骤**：按照平时操作的顺序，一步一步写清楚
3. **系统信息**：写清楚要用到哪些网站或软件，怎么登录
4. **数据说明**：举个具体例子，比如订单号、金额、日期等
5. **截图说明**：可以直接粘贴截图到表格里，或者单独保存文件

### 常见问题

**Q: 不知道重要程度怎么选？**
A: 想想如果这个工作不做了，会不会影响业务？影响大就选"非常重要"

**Q: 执行时间怎么选？**
A: 想想什么时候需要这个数据？比如每天上午要用的数据，就选择早上执行

**Q: 技术问题不懂怎么填？**
A: 重点写清楚业务需求，技术问题开发人员会协助解决

**Q: 操作步骤怎么写？**
A: 就像教别人做这个工作一样，一步一步写清楚每个操作

---

*本模版由广州融贸RPA项目管理小组制定，版本v1.2（简化版），最后更新时间：2025-08-14*
*如有疑问请联系RPA项目经理或通过内部协作平台咨询*
