# 公司内部NAS盘点分析报告

**创建日期：** 2025-08-21
**修订日期：** 2025-08-21
**分析范围：** 公司内部所有NAS存储设备

## 设备情况

| 操作系统  | 设备型号 | 存储容量 | 磁盘数量                    | 文件系统&阵列 | 网络地址     | 用户公司/部门                | 主要用途 |
| --------- | -------- | -------- | --------------------------- | ------------- | ------------ | ---------------------------- | -------- |
| 群晖(DSM) | DS1821+  | 69TiB    | 16TB * 6<br />400GB SSD * 2 | btrfs / RAID5 | 192.168.0.6  | 融晟                         |          |
| 群晖(DSM) | DS124    | 1.8TiB   | 2TB * 1                     | 条带          | 192.168.0.10 | 融科                         |          |
| 群晖(DSM) | DS124    | 1.8TiB   | 2TB * 1                     | 条带          | 192.168.0.20 | 澳新/财务                    |          |
| 群晖(DSM) | DS224    | 1.8TiB   | 2TB * 1                     | 条带          | 192.168.0.40 | 集团                         |          |
| 群晖(DSM) | DS124    | 7.2TiB   | 8TB * 1                     | 条带          | 192.168.0.50 | 派非顾/迅宇 (不归属集团管理) |          |

**设备统计：**

- 总设备数量：5台
- 总存储容量：约81.6TiB
- 主要品牌：群晖(Synology)
- 操作系统：DSM (DiskStation Manager)

---

## NAS容量需求评估

### 月增量数据


### 年增量数据


## 风险分析与评估

### 1. 数据安全风险

#### 🔴 高风险项

- **单点故障风险**：除DS1821+采用RAID5外，其他4台设备均使用条带模式，无冗余保护
- **数据丢失风险**：条带模式下硬盘故障将导致数据完全丢失
- **数据丢失风险**：未建立完善的数据备份和恢复机制, 存储池故障可能导致全部数据丢失

---

### 2. 网络安全风险

#### 🟡 中风险项

- **网络隔离不足**：所有设备位于同一网段(192.168.0.x)，公司内网均可访问缺乏有效隔离
- **访问控制缺失**：未明确各部门间的访问权限边界
- **外部访问风险**：可能存在未授权的远程访问通道
- **网络监控缺失**：缺乏对NAS设备网络流量的监控和日志

---

### 3. 管理风险

#### 🟡 中风险项

- **权限管理混乱**：机器数量较多, 无法统一用户和权限管理
- **审计日志不足**：缺乏完整的操作审计和日志记录
- **维护计划不足**：缺乏定期维护和更新计划
- **应急响应不足**：缺乏数据丢失或设备故障的应急预案

---

## 管理建议与改进措施

### 1. 数据保护改进

#### 立即执行

- **紧急备份**：为所有条带模式设备建立外部备份
- **财务数据特殊保护**：为财务或其他重要业务使用的目录/NAS建立每日备份机制

#### 中期规划

- **存储架构优化**：
  - 考虑将小容量设备整合，减少管理复杂度
  - 数据分级, 精细化管理存储池, 不可再生重要数据使用专用存储池管理
- **容灾备份**：建立异地备份或云备份机制
  - 使用云服务商的存储备份服务针对不可再生重要数据加密全量备份
  - 整合后下线的小容量单盘位设备, 考虑扩容后用于多机备份

---

### 2. 网络安全加固

#### 中期规划

- **网络分段**：

  - 财务数据独立VLAN
  - 各部门NAS按业务需求划分网段
- **访问控制**：

  - 实施基于角色的访问控制(RBAC)
  - 配置防火墙规则，限制跨部门访问
- **流量监控**：实施网络流量分析和异常检测
- **日志审计**：启用详细的操作日志记录和建立数据访问审计机制
- **权限梳理**：

  - 建立用户权限清单
  - 实施最小权限原则

---

## 实施优先级与时间表

### 🔴 第一优先级：数据安全保障（1-2周内完成）

**目标：** 消除数据丢失的直接风险

| 任务项目                 | 执行时间 |
| ------------------------ | -------- |
| 不可再生重要数据紧急备份 | 1周内    |
| 其他条带模式设备备份评估 | 2周内    |
| 数据重要性分级           | 2周内    |

### 🟡 第二优先级：架构优化（1-2个月内完成）

**目标：** 提升存储架构合理性和管理效率

| 任务项目               | 执行时间 |
| ---------------------- | -------- |
| 小容量设备整合方案设计 | 1个月内  |
| 存储池精细化管理       | 1个月内  |
| 云备份服务选型和部署   | 1个月内  |
| 下线设备改造为备份节点 | 2个月内  |

### 🟢 第三优先级：安全加固（2-3个月内完成）

**目标：** 完善网络安全和权限管理

| 任务项目               | 执行时间 |
| ---------------------- | -------- |
| 网络VLAN规划和实施     | 3个月内  |
| 用户权限清理和RBAC实施 | 3个月内  |
| 访问控制和防火墙配置   | 3个月内  |
| 日志审计和监控系统     | 3个月内  |

---

## 预算估算

### 详细预算分解

| 项目类别           | 具体项目   | 预估成本         | 说明             |
| ------------------ | ---------- | ---------------- | ---------------- |
| **数据备份** | 备份硬盘   | 4,000-12,000元   | 2-6块大容量硬盘  |
|                    | 云备份服务 | 3,000-6,000元/年 | 重要数据云端备份 |

### 分阶段投入预算

| 实施阶段         | 预算范围          | 主要投入                   |
| ---------------- | ----------------- | -------------------------- |
| 第一阶段（紧急） | 1-3万元           | 备份硬盘、云服务、人力     |
| 第二阶段（优化） | 0.5~1万元         | 存储整合、备份系统、人力   |
| 第三阶段（加固） | 0.5~1万元         | 安全软件、人力             |
| **总预算** | **2-4万元** | **根据实际需求调整** |

---

## 结论与建议

### 核心问题总结

当前NAS环境的主要问题集中在**数据安全风险**，4台设备采用条带模式无冗余保护，一旦硬盘故障将导致数据完全丢失。网络安全和管理规范问题相对次要，但也需要逐步改善。

---
