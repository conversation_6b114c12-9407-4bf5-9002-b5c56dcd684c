# gzrm-rpa-mgmt-knowledge-base

#### 项目工程介绍

广州融贸RPA项目管理知识库

## 项目背景介绍

## 1.1 立项背景

* 行业面，国内已有不少电商卖家在实际使用RPA类自动化工具，高效的辅助业务的日常运营，且效果不错（需要学习-参考-对标）
* "互联网"技术面，国内外大语言模型正处在高速的迭代区间，各种应用工具与AI的集成也在高速的扩充中（需要寻找公司内部可以落地的实际场景做切入）
* 公司内部运营及管理面，因我司业务模式的独特性及公司中长远的规划，面对逐步扩充的产品库，急需提效的解决方案（急需落地）

## 1.2 立项目的

* 通过接入RPA供应商（实在RPA），满足各部门优化现实存在的“重复”“低效”“费体力类”的人工操作
* 通过RPA的自动化流程落地，让大家把核心精力花费在更有价值的事项上
* 通过RPA的自动化流程落地的相关流程梳理及沉淀，让各部门及部门间协作更顺畅及标准化


# 二、项目管理小组角色定义

| 成员角色       | 责任事项                                                                                                                                                                           | 备注 |
| -------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ---- |
| 项目经理（PM） | 1、RPA项目管理框架制定及迭代<br />2、里程碑计划的制定及迭代<br />3、项目进度管理<br />4、项目整体汇报                                                                              |      |
| RPA项目架构师  | 1、负责整体RPA与融贸现有系统及后续规划所涉及的集成相关的技术架构方案输出、指导及阶段评估<br />2、负责整体RPA运行稳定性的评估及方案支持<br />3、负责复杂RPA流程的需求设计建议与实施 |      |
| 培训管理员     | 1、负责相关培训计划的制定<br />2、负责RPA培训知识库的管理<br />3、负责最佳实践CASE库管理                                                                                           |      |
| 项目运维       | 1、感知部门内相关已在线运行RPA流程的状态<br />2、负责本部门RPA流程的稳定性维护                                                                                                     |      |
| RPA开发者      | 1、负责按排期计划开发部门的自动化流程                                                                                                                                              |      |
