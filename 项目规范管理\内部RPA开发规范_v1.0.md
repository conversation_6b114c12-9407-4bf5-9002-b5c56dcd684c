---
文档标题: 内部RPA开发规范
创建日期: 2025-07-29
修订日期: 2025-07-30
版本: v1.0
适用范围: 广州融贸RPA项目组
RPA供应商: 实在RPA（实在智能）
---
# 内部RPA开发规范

## 1. 文档说明

本规范适用于广州融贸所有使用实在RPA进行流程开发的业务运营人员。规范设计考虑到开发者为非技术背景，力求简单易懂、便于执行。本规范涵盖流程命名、变量命名、元素命名、元素管理、变量管理、模块解耦设计和自定义组件建议等全方位开发指导。

### 1.1 规范等级说明

为帮助业务人员准确理解规范要求的重要程度，本文档采用以下3个等级标识：

- 🔴 **必须 (MUST)** - 强制性要求，必须严格遵守，违反将影响系统稳定性和管理效率
- 🟢 **推荐 (SHOULD)** - 强烈建议遵守，有助于提高开发质量和维护效率
- 🔵 **参考 (RECOMMEND)** - 推荐采用的最佳实践，有助于提升团队协作效率

### 1.2 适用范围与执行要求

🔴 **必须** - 所有RPA流程开发人员必须遵守本规范中标注为 [必须] 的条款
🟢 **推荐** - 鼓励团队成员采用"推荐"级别的最佳实践
🔵 **参考** - 团队成员可根据实际情况选择性采用

## 2. 命名规范

### 2.1 流程命名规范

#### 2.1.1 命名格式要求

🔴 **必须** - 所有RPA流程命名必须严格遵循以下格式：

```
[项目]_[部门名称]_[业务类型]_[优先级]_[调度类型]_[流程描述]_[版本号]
```

**✔️正例:**

```
汽配_运营_报表生成_L2_每月1次_分店铺下载亚马逊销售报告_v01
```

**❌反例:**

```
报表下载
```

#### 2.1.2 各段详细说明

##### [项目名称]

🔴 **必须** - 使用标准项目名称，不得自定义或缩写：

**项目名称列表（持续维护）**

- **汽配**: 汽配铺货项目
- **公共**: 公共通用

##### [部门名称]（中文）

🔴 **必须** - 使用标准部门中文名称，不得自定义或缩写：

**标准部门词列表（持续维护）**

- **供应链**: 供应链部门
- **市场**: 市场营销部门
- **客服**: 客服部门
- **财务**: 财务部门
- **技术**: IT技术部门
- **人事**: 人力资源部门
- **运营**: 运营部门
- **仓储**: 仓储部门
- **采购**: 采购部门

##### [业务类型]（中文）

🔴 **必须** - 使用预定义业务类型中文名称，确保分类准确性：

**预设业务词列表（持续维护）**

- **订单处理**: 订单相关的处理流程
- **库存管理**: 库存数据同步和管理流程
- **报表生成**: 各类报表的生成和统计流程
- **客户管理**: 客户信息维护和管理流程
- **财务处理**: 财务数据处理和对账流程
- **物流跟踪**: 物流信息跟踪和更新流程
- **产品管理**: 产品信息维护和管理流程
- **营销推广**: 营销活动和推广相关流程
- **质量管控**: 质量检查和管控流程
- **数据同步**: 系统间数据同步流程

##### [优先级]（2位）

🔴 **必须** - 准确评估并标注流程优先级：

**✔️正例:**

```
L1
```

**❌反例:**

```
重要
```

**标准结构:**

- **L1**: 最高优先级 - 影响核心业务，故障会导致业务中断，需要立即处理
- **L2**: 中等优先级 - 提升工作效率，故障影响有限，需要及时处理
- **L3**: 一般优先级 - 辅助功能，故障不影响主要业务，可延后处理

##### [调度类型]（中文）

🔴 **必须** - 明确描述这个流程会被怎样调度执行和触发的方式：

**定时执行类型：**

```
[单位] + [频次(数字)+次]
```

**✔️正例:**

```
每日1次
```

**❌反例:**

```
一天一次
```

**标准结构:**

- **每日1次**: 每天执行1次
- **每日2次**: 每天执行2次（如上午、下午各一次）
- **每日3次**: 每天执行3次（如每8小时一次）
- **每日4次**: 每天执行4次（如每6小时一次）
- **每日6次**: 每天执行6次（如每4小时一次）
- **每周1次**: 每周执行1次
- **每月1次**: 每月执行1次

**固定时点执行类型：**

```
[单位] + [时间点]
```

**✔️正例:**

```
每日8点
```

**❌反例:**

```
一天一次、晚上8点
```

**标准结构:**

- **每日8点**: 每天上午8:00执行
- **每日12点**: 每天中午12:00执行
- **每日18点**: 每天下午18:00执行
- **每日22点**: 每天晚上22:00执行
- **工作日9点**: 工作日上午9:00执行
- **工作日17点**: 工作日下午17:00执行

**其他触发类型：**

**✔️正例:**

```
手动
```

**❌反例:**

```
不做任何描述
```

**标准结构:**

- **手动**: 需要人工手动启动
- **事件**: 基于特定事件自动触发
- **实时监控**: 实时监控并处理

##### 流程描述（中文，简洁明了）

🔴 **必须** - 流程描述必须符合以下要求：

- 使用直观的中文描述流程的核心功能
- 严禁使用特殊符号和空格
- 采用"动宾结构"的语法

🟢 **推荐** - 使用业务术语，避免技术术语

**✔️正例:**

```
分店铺下载亚马逊销售报告
```

**❌反例:**

```
销售报表下载
```

##### 版本号（3位）

🔴 **必须** - 版本号管理规范：

- **V01**: 第1版（初始版本）
- **V02**: 第2版（功能优化）
- **V03**: 第3版（重大更新）
- 依此类推，不得跳跃版本号

#### 2.1.3 标准命名示例

🔵 **参考** - 以下为标准命名示例，建议参考：

```
供应链_订单处理_L1_每日8点_同步亚马逊订单到某ERP_V1
市场_报表生成_L2_每日一次_汇总某平台销售数据到共享盘_V2
客服_客户管理_L3_手动触发_更新某客户信息_V1
仓储_库存管理_L1_每日四次_同步xx库存数据到某地_V3
财务_财务处理_L2_每周一次_某平台财务对账处理_V1
技术_报表生成_L2_每月一次_某系统性能报告_V1
```

#### 2.1.4 命名验证检查

🟢 **推荐** - 流程命名完成后进行以下检查：

- [ ] 部门名称是否使用标准中文名称
- [ ] 业务类型是否准确选择
- [ ] 优先级评估是否合理（L1/L2/L3）
- [ ] 调度类型是否明确具体
- [ ] 流程描述是否简洁明了
- [ ] 版本号是否连续

#### 2.1.5 命名格式说明

🟢 **推荐** - 新格式的优势：

- **中文化**: 使用中文名称，业务人员更容易理解
- **直观性**: 一眼就能看出流程的归属部门和业务类型
- **层级化**: L1/L2/L3优先级更加清晰
- **时间明确**: 调度类型更加具体，包含固定时点执行

### 2.2 变量命名规范

#### 2.2.1 变量命名基本原则

🔴 **必须** - 所有变量命名必须遵循以下核心原则：

1. **见名知意**: 变量名称必须能够直接反映其用途和业务含义
2. **中文优先**: 优先使用中文命名，确保业务人员易于理解
3. **统一格式**: 严格遵循标准命名模式，不得随意变更
4. **避免歧义**: 禁止使用容易产生歧义或混淆的名称
5. **长度适中**: 在保证清晰表达的前提下，控制合理长度

#### 2.2.2 标准命名格式

🟢 **推荐** - 变量命名遵循以下格式：

```
[作用域前缀]_[业务含义]
```

##### 作用域前缀（红色为强制要求）

🟢 **推荐** - 根据变量使用范围选择对应前缀：

- **global_**: 🔴全局变量 - 整个流程生命周期内可访问的变量
- **config_**: 🔴配置变量 - 系统配置信息，运行期间通常保持不变
- **local_**: 🟢局部变量 - 仅在当前流程块可访问的变量, 为最常用的变量
- **input_**: 🟢输入参数 - 从外部系统或用户传入的参数
- **output_**: 🟢输出结果 - 返回给外部系统或用户的结果
- **temp_**: 🟢临时变量 - 仅在当前处理步骤中使用的临时存储
- **count_**: 🟢计数变量 - 用于计数统计的专用变量
- **status_**: 🟢状态变量 - 标识流程或操作状态的变量
- **loop_**: 🟢循环变量 - 在循环结构中使用的控制变量

#### 2.2.3 标准命名示例

**✔️正例:**

```
global_系统当前时间
config_易仓ERP登录地址
input_店铺名称
output_下载处理结果
temp_文件下载路径
count_处理成功数量
status_流程执行状态
loop_当前月份
紫鸟店铺账号名称
```

**❌反例:**

```
临时变量
变量1
xxx
```

#### 2.2.4 变量命名质量检查

🟢 **推荐** - 变量创建后进行以下质量检查：

- [ ] 作用域前缀是否正确
- [ ] 业务含义是否清晰
- [ ] 数据类型后缀是否匹配
- [ ] 命名是否存在歧义
- [ ] 长度是否适中（建议15-30个字符）

### 2.3 元素命名规范

#### 2.3.1 元素命名基本原则

🔴 **必须** - 所有页面元素命名必须遵循以下原则：

1. **描述性命名**: 元素名称必须能够清晰描述其在页面中的功能作用
2. **层级结构**: 必须体现元素在页面中的层级关系和归属
3. **业务相关**: 必须结合具体业务场景进行命名
4. **避免重复**: 同一流程中严禁出现重复的元素名称

🟢 **推荐** - 元素命名应考虑后续维护和团队协作的便利性

#### 2.3.2 标准命名格式

🔴 **必须** - 元素命名必须严格遵循以下格式：

```
[软件名称(🟢可选)]_[页面标识]_[元素描述]_[元素类型]
```

##### 软件名称

🟢 **推荐** - 同一个页面可能在不同浏览器或者有桌面版等入口, 清晰标注软件名称方便维护：

- **紫鸟**: 紫鸟v5浏览器
- **chrome**: Google浏览器
- **钉钉**: 钉钉
- **飞书**: 飞书

##### 页面标识

🔴 **必须** - 必须标记该元素可以在哪些页面下生效：
通常直接使用浏览器tab标题, 或者使用该功能的标题页面

- **通用**: 任何页面均可访问的通用元素
- **登录页**: 登录页
- **亚马逊主页**: 亚马逊主页
- **报表页**: 报表下载页面

##### 元素描述

🔴 **必须** - 元素描述必须符合以下要求：

1. **中文优先**: 优先使用中文描述元素功能
2. **见名知意**: 元素名称必须能够直接反映其用途和业务含义
3. **避免歧义**: 禁止使用容易产生歧义或混淆的名称
4. **长度适中**: 在保证清晰表达的前提下，控制合理长度

##### 元素类型

🔴 **必须** - 根据元素功能选择对应类型代码：

- **按钮**: 按钮（Button）包含常规按钮以及所有可点击元素
- **文本框**: 文本框（Text Input）包含常规文本块以及所有可输入元素
- **标签**: 标签/文本（Label）通常页面上那些点击后无功能的区域, 如提示窗, 一级菜单等悬停交互元素
- **下拉列表**: 下拉列表（Drop Down List）
- **复选框**: 复选框（Checkbox）
- **单选框**: 单选框（Radio Button）
- **表格**: 表格（Table）
- **链接**: 链接（Link）特指链接, 可使用[按钮]代替
- **图片**: 图片（Image）
- **元素**: 无明确归类的元素

#### 2.3.3 标准命名示例

**✔️正例:**

```
紫鸟_亚马逊订单报告_下载_按钮
钉钉_群聊页_发送消息_按钮
chrome_京东商品列表_商品列表区域_元素
```

**❌反例:**

```
按钮_导航菜单 // 实在自动生成的样式
```

### 2.4 元素管理规范

#### 2.4.1 元素库管理要求

🔴 **必须** - 元素库管理的强制性要求：

1. **统一存储**: 所有页面元素必须统一存储在指定的元素库中
2. **分类管理**: 必须按照系统/模块进行严格分类管理
3. **版本控制**: 元素变更时必须保留历史版本记录
4. **定期维护**: 必须定期检查和更新失效元素, 清理无引用的元素

🟢 **推荐** - 建立元素库管理员制度，专人负责维护

#### 2.4.2 标准分类结构

🟢 **推荐** - 使用实在RPA的元素分组功能, 采用以下标准分类结构：

```
superbrowser
├── 亚马逊卖家中心
│   ├── 登录模块
│   ├── 订单管理
│   ├── 库存管理
│   └── 报表中心
├── ERP系统
│   ├── 订单处理
│   ├── 库存管理
│   ├── 财务管理
│   └── 系统设置
└── 第三方工具
    ├── 邮件系统
    ├── 客服系统
    └── 数据分析工具
```

#### 2.4.3 元素维护操作规范

🔴 **必须** - 元素维护操作的强制性要求：

1. **新增元素**: 🔴 必须严格按照命名规范添加新元素
2. **修改元素**: 🟢 详细记录修改原因、时间和操作人员
3. **删除元素**: 🔴 必须确认无任何流程使用后才能删除
4. **测试验证**: 🔴 元素变更后必须完成相关流程的测试验证

🔵 **可选** - 建立元素变更审批流程

#### 2.4.4 元素复用管理原则

🟢 **推荐** - 遵循以下元素复用原则：

1. **优先复用**: 新流程开发时优先使用已有元素
2. **避免重复**: 严禁创建功能相同的重复元素
3. **标准化**: 相同类型的元素使用统一的定位方式
4. **文档记录**: 详细记录元素的使用场景和注意事项

🔵 **可选** - 可建立元素使用统计报告，分析复用率

### 2.5 变量管理规范

#### 2.5.1 变量生命周期管理

1. **创建阶段**: 按照命名规范创建变量
2. **使用阶段**: 合理使用，避免滥用全局变量
3. **维护阶段**: 定期检查变量使用情况
4. **清理阶段**: 及时清理无用变量

#### 2.5.2 变量作用域管理

```
全局变量 (Global)
├── 系统配置类 (config_*)
├── 公共数据类 (global_*)
└── 状态标识类 (status_*)

流程变量 (Process)
├── 输入参数类 (input_*)
├── 输出结果类 (output_*)
└── 临时处理类 (temp_*)

循环变量 (Loop)
├── 循环计数类 (loop_*)
├── 循环数据类 (loop_*)
└── 循环状态类 (loop_*)
```

#### 2.5.3 变量初始化规范

1. **必须初始化**: 所有变量使用前必须初始化
2. **合理默认值**: 设置符合业务逻辑的默认值
3. **类型匹配**: 初始值类型要与变量类型匹配
4. **注释说明**: 复杂变量要添加初始化说明

#### 2.5.4 变量使用最佳实践

```
✅ 推荐做法:
- 使用有意义的变量名
- 及时清理临时变量
- 避免全局变量污染
- 合理设置变量作用域

❌ 避免做法:
- 使用无意义的变量名 (如: a, b, c)
- 重复使用同一变量存储不同类型数据
- 过度使用全局变量
- 不初始化就使用变量
```

## 3. 模块解耦设计规范 🔵 **可选**

#### 3.1 模块化设计原则

1. **单一职责**: 每个模块只负责一个特定功能
2. **高内聚**: 模块内部功能紧密相关
3. **低耦合**: 模块之间依赖关系最小化
4. **可复用**: 模块可以在不同流程中复用

#### 3.1.2 模块接口设计

```
模块接口规范:
输入参数: 明确定义输入参数的类型和格式
输出结果: 明确定义输出结果的结构
异常处理: 定义可能的异常情况和处理方式
使用说明: 提供详细的使用文档和示例
```

## 4. 自定义组件建议 🟢 **推荐**

### 4.1 组件开发场景

1. **重复性高**: 多个流程中都需要使用的功能
2. **逻辑复杂**: 包含复杂业务逻辑的操作
3. **标准化**: 需要统一处理方式的操作
4. **维护性**: 需要集中维护的功能

### 4.2 推荐自定义组件

```
系统集成组件:
├── 共享盘存储组件
├── 账密获取组件
└── 消息通知组件

业务逻辑组件:
├── 进入某业务系统功能模块组件
└── 日期选择组件
```

### 4.3 组件开发规范

1. **输入验证**: 对输入参数进行严格验证
2. **异常处理**: 完善的异常处理机制
3. **日志记录**: 记录关键操作和结果
4. **性能优化**: 考虑组件的执行效率
5. **文档完整**: 提供详细的使用说明

## 5. 流程开发最佳实践

### 5.1 标准开发流程

🔴 **必须** - 所有RPA流程开发必须遵循以下标准流程：

1. **需求分析**: 必须明确业务需求和技术要求
2. **设计规划**: 必须进行模块化设计和架构规划
3. **编码开发**: 必须严格按照本规范进行流程开发
4. **测试验证**: 必须完成单元测试和集成测试
5. **部署上线**: 必须按照标准上线流程部署到生产环境
6. **运维监控**: 必须持续监控和优化流程性能

### 5.2 质量控制检查清单

🔴 **必须** - 流程上线前必须通过以下检查：

- [ ] 流程命名符合规范
- [ ] 变量命名符合规范
- [ ] 元素命名符合规范
- [ ] 模块设计合理
- [ ] 异常处理完整
- [ ] 日志记录清晰
- [ ] 性能满足要求
- [ ] 文档编写完整

### 5.3 规范执行监督

🟡 **建议** - 建立规范执行监督机制：

- 定期进行规范执行情况检查
- 建立规范违规处理流程
- 组织规范培训和交流
- 收集规范改进建议

## 6. 附录

### 6.1 实在RPA相关资源

- **官网**: https://www.ai-indeed.com/
- **帮助文档**: https://rpa-college.ai-indeed.com/doc
- **社区论坛**: [待补充]
- **技术支持**: [待补充]

### 6.2 常用业务系统

- **亚马逊卖家中心**: https://sellercentral.amazon.com/
- **速卖通卖家中心**: https://sell.aliexpress.com/
- **Shopee卖家中心**: https://seller.shopee.cn/
- **领星ERP**: [具体地址]
- **易仓ERP**: [具体地址]

### 6.3 联系方式

- **RPA项目架构师**: [待补充]
- **培训管理员**: [待补充]
- **技术支持**: [待补充]
- **业务对接人**: [待补充]

## 7. 规范等级执行指南

### 7.1 强制性要求（🔴 必须）

以下规范条款为强制性要求，所有RPA开发人员必须严格遵守：

**流程命名规范**

- 必须使用标准命名格式：[部门名称]_[业务类型]_[优先级]_[调度类型]_[流程描述]_[版本号]
- 必须使用标准部门中文名称，不得自定义或缩写
- 必须使用预定义业务类型中文名称，确保分类准确性
- 必须准确评估并标注流程优先级（L1/L2/L3）
- 必须明确描述调度类型和触发方式
- 流程描述必须使用中文、严禁特殊符号和空格、采用"动宾结构"语法
- 必须按照V1、V2、V3顺序管理版本号，不得跳跃

**变量命名规范**

- 必须遵循核心原则：见名知意、中文优先、统一格式、避免歧义、长度适中
- 必须使用标准格式：[作用域前缀]_[业务含义]
- global_（全局变量）和config_（配置变量）前缀为强制要求
- 所有变量使用前必须初始化

**元素命名规范**

- 必须遵循基本原则：描述性命名、层级结构、业务相关、避免重复
- 必须严格遵循格式：[软件名称(可选)]_[页面标识]_[元素描述]_[元素类型]
- 必须标记该元素可以在哪些页面下生效
- 元素描述必须中文优先、见名知意、避免歧义、长度适中
- 必须根据元素功能选择对应类型代码
- 同一流程中严禁出现重复元素名称

**元素管理规范**

- 必须将所有页面元素统一存储在指定的元素库中
- 必须按照系统/模块进行严格分类管理
- 元素变更时必须保留历史版本记录
- 必须定期检查和更新失效元素，清理无引用的元素
- 新增元素必须严格按照命名规范添加
- 删除元素必须确认无任何流程使用后才能删除
- 元素变更后必须完成相关流程的测试验证

**开发流程规范**

- 必须遵循标准开发流程：需求分析→设计规划→编码开发→测试验证→部署上线→运维监控
- 流程上线前必须通过质量控制检查清单的所有项目

### 7.2 推荐实践（🟢 推荐）

以下为推荐采用的最佳实践，有助于提高开发质量和维护效率：

**流程命名方面**

- 建议参考标准命名示例进行命名
- 流程命名完成后进行验证检查
- 使用业务术语，避免技术术语

**变量命名方面**

- 推荐使用作用域前缀：local_、input_、output_、temp_、count_、status_、loop_
- 变量创建后进行质量检查
- 合理设置变量作用域，避免全局变量污染

**元素命名方面**

- 推荐标注软件名称，方便维护
- 元素命名应考虑后续维护和团队协作的便利性

**元素管理方面**

- 建议建立元素库管理员制度，专人负责维护
- 推荐使用实在RPA的元素分组功能，采用标准分类结构
- 遵循元素复用管理原则：优先复用、避免重复、标准化、文档记录
- 详细记录元素修改原因、时间和操作人员

**开发流程方面**

- 建议建立规范执行监督机制

### 7.3 参考实践（🔵 参考）

以下为推荐采用的最佳实践，有助于提升团队协作效率，可根据实际情况选择性采用：

**流程命名方面**

- 参考提供的标准命名示例

**元素管理方面**

- 可建立元素变更审批流程
- 可建立元素使用统计报告，分析复用率

**模块设计方面**

- 模块解耦设计规范（单一职责、高内聚、低耦合、可复用）

**组件开发方面**

- 推荐自定义组件的开发场景和规范

### 7.5 违规处理机制

🔴 **必须** - 对于违反强制性规范的情况：

- 第一次违规：口头提醒并要求立即整改
- 第二次违规：书面警告并强制培训
- 第三次违规：上报项目经理处理

🟢 **建议** - 对于未遵守建议性规范的情况：

- 定期提醒和指导
- 在团队会议中分享最佳实践

---

*本规范由广州融贸RPA项目管理小组制定，版本v1.0，最后更新时间：2025-07-30*
*如有疑问或建议，请联系项目经理或通过内部协作平台反馈*

**重要提醒**:

- 🔴 标识的规范为强制性要求，必须严格执行
- 🟢 标识的规范为推荐实践，应当积极采用
- 🔵 标识的规范为可选采用，可根据情况决定
